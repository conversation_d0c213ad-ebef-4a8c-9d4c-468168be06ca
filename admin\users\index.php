<?php
/**
 * Admin User Management - List
 */
require_once '../../app/config/config.php';
require_once '../../app/config/database.php';
require_once '../../app/helpers.php';
require_once '../../app/models/User.php';

// Check if user is admin
if (!is_admin()) {
    set_error_message('Bạn không có quyền truy cập trang này');
    redirect(BASE_URL);
}

// Connect to database
$db = new Database();
$conn = $db->getConnection();

// Initialize models
$userModel = new User($conn);

// Handle search query
$search = '';
$roleFilter = '';
if (isset($_GET['search'])) {
    $search = sanitize($_GET['search']);
}
if (isset($_GET['role'])) {
    $roleFilter = sanitize($_GET['role']);
}

// Pagination
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$perPage = 10;
$offset = ($page - 1) * $perPage;

// Initialize variables to prevent undefined variable errors
$users = [];
$totalItems = 0;

// Get users with filters
if (!empty($search) || !empty($roleFilter)) {
    // Build conditions for filtered search
    $conditions = [];
    $params = [];

    if (!empty($search) && trim($search) !== '') {
        $conditions[] = "(u.username LIKE :search OR u.full_name LIKE :search OR u.email LIKE :search)";
        $params[':search'] = "%{$search}%";
    }

    if (!empty($roleFilter) && trim($roleFilter) !== '' && $roleFilter !== '0') {
        $conditions[] = "u.role_id = :role_id";
        $params[':role_id'] = $roleFilter;
    }

    // Only proceed if we have actual search conditions
    if (!empty($conditions)) {
        $whereClause = 'WHERE ' . implode(' AND ', $conditions);

        // Get users with filters using string interpolation for LIMIT/OFFSET (safer)
        $sql = "SELECT u.*, r.role_name
                FROM users u
                JOIN roles r ON u.role_id = r.role_id
                {$whereClause}
                ORDER BY u.created_at DESC
                LIMIT {$perPage} OFFSET {$offset}";

        $stmt = $conn->prepare($sql);
        foreach ($params as $key => $value) {
            $stmt->bindValue($key, $value);
        }
        $stmt->execute();
        $users = $stmt->fetchAll();

        // Count total for pagination
        $countSql = "SELECT COUNT(*) as count FROM users u {$whereClause}";
        $countStmt = $conn->prepare($countSql);
        foreach ($params as $key => $value) {
            $countStmt->bindValue($key, $value);
        }
        $countStmt->execute();
        $totalItems = $countStmt->fetch()['count'];
    } else {
        // No valid search conditions - fall back to showing all users
        $users = $userModel->getAllWithRoles('u.created_at DESC', $perPage, $offset);
        $totalItems = $userModel->count();
    }
} else {
    // No filters - use model method to get all users
    $users = $userModel->getAllWithRoles('u.created_at DESC', $perPage, $offset);
    $totalItems = $userModel->count();
}

// Ensure users is always an array
if (!is_array($users)) {
    $users = [];
}

// Calculate total pages
$totalPages = ceil($totalItems / $perPage);

// Handle delete action if confirmed
if (isset($_POST['delete']) && isset($_POST['user_id']) && verify_csrf_token($_POST['csrf_token'] ?? '')) {
    $userId = (int)$_POST['user_id'];
    $user = $userModel->getById($userId);
    
    if ($user) {
        // Prevent deleting current admin user
        $currentUser = get_logged_in_user();
        if ($userId == $currentUser['user_id']) {
            set_error_message('Bạn không thể xóa tài khoản của chính mình');
        } else {
            if ($userModel->delete($userId)) {
                set_success_message('Người dùng đã được xóa thành công');
            } else {
                set_error_message('Không thể xóa người dùng');
            }
        }
    } else {
        set_error_message('Người dùng không tồn tại');
    }
    
    redirect(ADMIN_URL . '/users/index.php');
}

// Get roles for filter dropdown
$sql = "SELECT * FROM roles ORDER BY role_name";
$stmt = $conn->prepare($sql);
$stmt->execute();
$roles = $stmt->fetchAll();

// Set page title
$pageTitle = 'Quản lý người dùng';

// Include content in layout
$content = VIEWS_DIR . '/admin/users/index.php';
include_once VIEWS_DIR . '/layouts/main.php';
