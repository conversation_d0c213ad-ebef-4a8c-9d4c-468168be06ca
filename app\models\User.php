<?php
require_once APP_DIR . '/models/BaseModel.php';

/**
 * User Model
 */
class User extends BaseModel {
    /**
     * Constructor
     *
     * @param PDO $db Database connection
     */
    public function __construct($db) {
        parent::__construct($db, 'users');
        $this->primaryKey = 'user_id';
    }

    /**
     * Get all users with their roles
     *
     * @param string $orderBy Order by clause
     * @param int $limit Limit results
     * @param int $offset Offset for pagination
     * @return array Users
     */
    public function getAllWithRoles($orderBy = 'u.username', $limit = null, $offset = null) {
        $sql = "SELECT u.*, r.role_name
                FROM users u
                JOIN roles r ON u.role_id = r.role_id";

        if ($orderBy) {
            $sql .= " ORDER BY {$orderBy}";
        }

        $params = [];

        if ($limit) {
            $sql .= " LIMIT :limit";
            $params[':limit'] = $limit;

            if ($offset) {
                $sql .= " OFFSET :offset";
                $params[':offset'] = $offset;
            }
        }

        $stmt = $this->conn->prepare($sql);

        foreach ($params as $key => $value) {
            $stmt->bindValue($key, $value, PDO::PARAM_INT);
        }

        $stmt->execute();

        return $stmt->fetchAll();
    }

    /**
     * Get user by username
     *
     * @param string $username Username
     * @return array|null User or null if not found
     */
    public function getByUsername($username) {
        return $this->findOneBy('username = :username', [':username' => $username]);
    }

    /**
     * Get user by email
     *
     * @param string $email Email
     * @return array|null User or null if not found
     */
    public function getByEmail($email) {
        return $this->findOneBy('email = :email', [':email' => $email]);
    }

    /**
     * Create a new user
     *
     * @param array $data User data
     * @return int|bool User ID or false on failure
     */
    public function create($data) {
        // Hash password if provided
        if (isset($data['password'])) {
            $data['password'] = password_hash($data['password'], PASSWORD_DEFAULT);
        }
        
        return parent::create($data);
    }

    /**
     * Update user
     *
     * @param int $id User ID
     * @param array $data User data
     * @return bool True on success, false on failure
     */
    public function update($id, $data) {
        // Hash password if provided
        if (isset($data['password']) && !empty($data['password'])) {
            $data['password'] = password_hash($data['password'], PASSWORD_DEFAULT);
        } else {
            // Don't update password if empty
            unset($data['password']);
        }
        
        return parent::update($id, $data);
    }

    /**
     * Check if username exists
     *
     * @param string $username Username
     * @param int $excludeId Exclude user ID (for updates)
     * @return bool True if username exists
     */
    public function usernameExists($username, $excludeId = null) {
        $sql = "SELECT COUNT(*) as count FROM users WHERE username = :username";
        
        if ($excludeId) {
            $sql .= " AND user_id != :excludeId";
        }
        
        $stmt = $this->conn->prepare($sql);
        $stmt->bindValue(':username', $username);
        
        if ($excludeId) {
            $stmt->bindValue(':excludeId', $excludeId);
        }
        
        $stmt->execute();
        $result = $stmt->fetch();
        
        return $result['count'] > 0;
    }

    /**
     * Check if email exists
     *
     * @param string $email Email
     * @param int $excludeId Exclude user ID (for updates)
     * @return bool True if email exists
     */
    public function emailExists($email, $excludeId = null) {
        $sql = "SELECT COUNT(*) as count FROM users WHERE email = :email";
        
        if ($excludeId) {
            $sql .= " AND user_id != :excludeId";
        }
        
        $stmt = $this->conn->prepare($sql);
        $stmt->bindValue(':email', $email);
        
        if ($excludeId) {
            $stmt->bindValue(':excludeId', $excludeId);
        }
        
        $stmt->execute();
        $result = $stmt->fetch();
        
        return $result['count'] > 0;
    }

    /**
     * Create password reset token
     *
     * @param string $email User email
     * @return string|bool Reset token or false on failure
     */
    public function createPasswordResetToken($email) {
        $user = $this->getByEmail($email);
        
        if (!$user) {
            return false;
        }
        
        $token = bin2hex(random_bytes(32));
        $expires = date('Y-m-d H:i:s', strtotime('+1 hour'));
        
        $sql = "UPDATE users 
                SET reset_token = :token, reset_token_expires = :expires 
                WHERE email = :email";
        
        $stmt = $this->conn->prepare($sql);
        $stmt->bindValue(':token', $token);
        $stmt->bindValue(':expires', $expires);
        $stmt->bindValue(':email', $email);
        
        if ($stmt->execute()) {
            return $token;
        }
        
        return false;
    }

    /**
     * Verify password reset token
     *
     * @param string $token Reset token
     * @return array|bool User data or false if token is invalid
     */
    public function verifyPasswordResetToken($token) {
        $sql = "SELECT * FROM users 
                WHERE reset_token = :token 
                AND reset_token_expires > NOW() 
                LIMIT 1";
        
        $stmt = $this->conn->prepare($sql);
        $stmt->bindValue(':token', $token);
        $stmt->execute();
        
        if ($stmt->rowCount() > 0) {
            return $stmt->fetch();
        }
        
        return false;
    }

    /**
     * Reset password using token
     *
     * @param string $token Reset token
     * @param string $newPassword New password
     * @return bool True on success, false on failure
     */
    public function resetPassword($token, $newPassword) {
        $user = $this->verifyPasswordResetToken($token);
        
        if (!$user) {
            return false;
        }
        
        $hashedPassword = password_hash($newPassword, PASSWORD_DEFAULT);
        
        $sql = "UPDATE users 
                SET password = :password, reset_token = NULL, reset_token_expires = NULL 
                WHERE reset_token = :token";
        
        $stmt = $this->conn->prepare($sql);
        $stmt->bindValue(':password', $hashedPassword);
        $stmt->bindValue(':token', $token);
        
        return $stmt->execute();
    }
}